import os
import json
import logging
from pathlib import Path
from datetime import datetime
import fitz
import pandas as pd
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger(__name__)

class NativePDFProcessor:
    def __init__(self, config):
        self.config = config
    
    def extract_text_blocks(self, page):
        blocks = page.get_text("dict")["blocks"]
        if self.config.get("debug", False):
            logger.info(f"Page {page.number + 1}: 发现 {len(blocks)} 个原始文本块")
        
        page_height, page_width = page.rect.height, page.rect.width
        header_threshold, footer_threshold = page_height * 0.05, page_height * 0.95
        extreme_margin = page_width * 0.02
        filtered_blocks = []
        
        for block in blocks:
            if "lines" not in block or not block["lines"]: continue
            bbox = block["bbox"]
            if self.config.get("filter_headers_footers", True) and (bbox[1] < header_threshold or bbox[3] > footer_threshold): continue
            if bbox[0] < extreme_margin or bbox[2] > page_width - extreme_margin: continue
            filtered_blocks.append(block)
        return filtered_blocks

    def extract_tables(self, page):
        tables = []
        if not self.config.get("extract_tables", True): return tables
        try:
            for i, table in enumerate(page.find_tables()):
                table_data = table.extract()
                if table_data and len(table_data) > 1:
                    df = pd.DataFrame(table_data[1:], columns=table_data[0])
                    html_content = df.to_html(index=False, border=1)
                    tables.append({"table_id": f"table_{i}", "html_content": html_content, "bbox": table.bbox})
        except Exception as e:
            logger.warning(f"在页面 {page.number + 1} 提取表格失败: {e}")
        return tables

    def extract_images(self, page, img_dir, page_num):
        images = []
        if not self.config.get("extract_images", True):
            return images
            
        try:
            for i, img_info in enumerate(page.get_images(full=True)):
                xref = img_info[0]
                pix = fitz.Pixmap(page.parent, xref)
                
                if pix.colorspace is None:
                    logger.warning(f"在页面 {page_num} 发现一张图片 (xref: {xref}) 缺少色彩空间信息，已跳过。")
                    pix = None # 释放内存
                    continue

                # 过滤小图片
                if len(pix.samples) < self.config.get("min_image_size_bytes", 1024):
                    pix = None
                    continue

                if pix.colorspace.name not in (fitz.csRGB.name, fitz.csGRAY.name):
                    logger.debug(f"检测到非RGB/GRAY图片 (colorspace: {pix.colorspace.name}), 转换为RGB...")
                    pix = fitz.Pixmap(fitz.csRGB, pix)

                img_filename = f"page_{page_num}_img_{i}.png"
                img_path = Path(img_dir) / img_filename
                pix.save(str(img_path))
                
                bbox_obj = page.get_image_bbox(img_info)
                bbox_coords = (bbox_obj.x0, bbox_obj.y0, bbox_obj.x1, bbox_obj.y1)
                
                images.append({
                    "image_id": f"img_{i}", "filename": img_filename,
                    "bbox": bbox_coords, "size": (pix.width, pix.height)
                })
                pix = None

        except Exception as e:
            logger.warning(f"在页面 {page_num} 提取图片时发生意外错误: {e}", exc_info=True)
        return images
    
    def process_pdf(self, pdf_path, output_dir):
        pdf_name = Path(pdf_path).stem
        output_dir_path = Path(output_dir)
        output_dir_path.mkdir(exist_ok=True, parents=True)
        img_dir = output_dir_path / "imgs"
        img_dir.mkdir(exist_ok=True)
        jsonl_path = output_dir_path / f"{pdf_name}.jsonl"
        md_path = output_dir_path / f"{pdf_name}.md"
        
        try:
            with fitz.open(pdf_path) as doc, \
                 open(jsonl_path, 'w', encoding='utf-8') as jsonl_file, \
                 open(md_path, 'w', encoding='utf-8') as md_file:
                logger.info(f"正在处理: {pdf_name} ({len(doc)} 页)")
                md_file.write(f"# {pdf_name}\n\n")
                
                for page in doc:
                    page_num = page.number + 1
                    text_blocks = self.extract_text_blocks(page)
                    tables = self.extract_tables(page)
                    images = self.extract_images(page, img_dir, page_num)
                    
                    content_blocks_for_json = []
                    for block in text_blocks:
                        text_content = "".join(span["text"] for line in block["lines"] for span in line["spans"]).strip()
                        if text_content:
                            content_blocks_for_json.append({"block_type": "text", "content": text_content, "bbox": block["bbox"]})
                    
                    entry = {"document_id": pdf_name, "page_number": page_num, "timestamp": datetime.now().isoformat(), "content_blocks": content_blocks_for_json, "tables": tables, "images": images}
                    jsonl_file.write(json.dumps(entry, ensure_ascii=False) + '\n')
                    
                    md_file.write(f"## Page {page_num}\n\n")
                    for block in content_blocks_for_json: md_file.write(f"{block['content']}\n\n")
                    for table in tables: md_file.write(f"{table['html_content']}\n\n")
                    for image in images: md_file.write(f"![{image['filename']}](imgs/{image['filename']})\n\n")
                    
                    logger.debug(f"已处理页面 {page_num}/{len(doc)}")
        except Exception as e:
            logger.error(f"处理文件 '{pdf_name}' 时发生严重错误: {e}", exc_info=True)

def main():
    config = {
        "input_dir": "./input_native", "output_root_dir": "./output_native",
        "filter_headers_footers": True, "extract_tables": True, "extract_images": True,
        "min_image_size_bytes": 1024, "debug": False
    }
    processor = NativePDFProcessor(config)
    input_path = Path(config["input_dir"])
    output_root_path = Path(config["output_root_dir"])
    
    if not input_path.is_dir():
        logger.error(f"输入目录不存在: '{input_path}'。")
        os.makedirs(input_path, exist_ok=True)
        return

    pdf_files = list(input_path.glob("*.pdf"))
    if not pdf_files:
        logger.warning(f"在目录 '{input_path}' 中没有找到任何PDF文件。")
        return
        
    logger.info(f"在 '{input_path}' 中发现 {len(pdf_files)} 个PDF文件，开始处理...")
    
    for pdf_file in pdf_files:
        try:
            doc_name = pdf_file.stem
            doc_output_dir = output_root_path / doc_name
            logger.info(f"--- 开始处理: {pdf_file.name} ---")
            processor.process_pdf(str(pdf_file), str(doc_output_dir))
            logger.info(f"--- ✅ 处理完成: {pdf_file.name} -> 输出到 '{doc_output_dir}' ---\n")
        except Exception as e:
            logger.error(f"❌ 处理文件 {pdf_file.name} 时发生顶层错误，已跳过: {e}", exc_info=True)

if __name__ == '__main__':
    main()