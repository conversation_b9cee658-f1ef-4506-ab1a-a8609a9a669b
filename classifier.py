import fitz
import os
import shutil
from pathlib import Path

def classify_pdf(pdf_path, text_threshold=100, pages_to_check=3):
    """
    对单个PDF文件进行分类，判断其是'native'还是'scanned'
    """
    try:
        doc = fitz.open(pdf_path)
        total_text_length = 0
        num_pages = min(len(doc), pages_to_check)
        if num_pages == 0:
            doc.close()
            return 'empty'
        for page_num in range(num_pages):
            page = doc.load_page(page_num)
            total_text_length += len(page.get_text())
            if total_text_length > text_threshold:
                doc.close()
                return 'native'
        doc.close()
        return 'native' if total_text_length > text_threshold else 'scanned'
    except Exception as e:
        print(f"处理文件 {pdf_path} 时发生错误: {e}")
        return 'error'

def organize_pdfs_into_folders(source_dir, native_target_dir, scanned_target_dir):
    """
    扫描源目录，根据PDF类型将其移动到相应的目标目录。
    """
    print("--- PDF文件归类程序启动 ---")

    # --- 1. 检查并创建目录 ---
    source_path = Path(source_dir)
    native_path = Path(native_target_dir)
    scanned_path = Path(scanned_target_dir)

    if not source_path.is_dir():
        print(f"错误: 源目录 '{source_dir}' 不存在。请创建并放入PDF文件。")
        return

    # 使用 exist_ok=True, 即使目录已存在也不会报错
    native_path.mkdir(parents=True, exist_ok=True)
    scanned_path.mkdir(parents=True, exist_ok=True)
    print(f"源目录: {source_path}")
    print(f"原生PDF目标目录: {native_path}")
    print(f"扫描PDF目标目录: {scanned_path}\n")

    # --- 2. 获取所有PDF文件 ---
    pdf_files = list(source_path.glob("*.pdf"))
    if not pdf_files:
        print("在源目录中没有找到PDF文件。")
        return
        
    print(f"找到 {len(pdf_files)} 个PDF文件，开始分类和移动...\n")
    
    # --- 3. 遍历、分类和移动 ---
    counts = {"native": 0, "scanned": 0, "error": 0, "skipped": 0}
    for pdf_file in pdf_files:
        # a) 分类
        pdf_type = classify_pdf(str(pdf_file))
        
        # b) 根据分类结果确定目标路径
        destination_path = None
        if pdf_type == 'native':
            destination_path = native_path / pdf_file.name
            counts['native'] += 1
        elif pdf_type == 'scanned':
            destination_path = scanned_path / pdf_file.name
            counts['scanned'] += 1
        else:
            print(f"❗️ 文件 '{pdf_file.name}' 类型未知或处理错误，已跳过。")
            counts['error'] += 1
            continue

        # c) 检查目标位置是否已存在同名文件
        if destination_path.exists():
            print(f"🟡 文件 '{pdf_file.name}' 在目标目录已存在，已跳过。")
            counts['skipped'] += 1
            continue

        # d) 执行移动操作
        try:
            shutil.move(str(pdf_file), str(destination_path))
            print(f"✅ 已移动 '{pdf_file.name}' -> 到 '{destination_path.parent.name}' 目录")
        except Exception as e:
            print(f"❌ 移动文件 '{pdf_file.name}' 失败: {e}")
            counts[pdf_type] -= 1 # 移动失败，从计数中减去
            counts['error'] += 1

    # --- 4. 打印总结报告 ---
    print("\n--- 整理完成 ---")
    print(f"移动到 '原生' 目录: {counts['native']} 个")
    print(f"移动到 '扫描' 目录: {counts['scanned']} 个")
    print(f"因目标已存在而跳过: {counts['skipped']} 个")
    print(f"因错误而跳过: {counts['error']} 个")

if __name__ == '__main__':
    # --- 配置文件夹路径 ---
    # 1. 待分类的PDF
    SOURCE_DIRECTORY = './input_all'
    
    # 2. 分类后的原生PDF
    NATIVE_PDF_DIRECTORY = './inputs_native'
    
    # 3. 分类后的扫描PDF
    SCANNED_PDF_DIRECTORY = './inputs_scanned'
    
    # (如果文件夹不存在，会自动创建)
    if not os.path.exists(SOURCE_DIRECTORY):
        os.makedirs(SOURCE_DIRECTORY)
        print(f"创建了源文件夹 '{SOURCE_DIRECTORY}'。请将您要分类的PDF文件放入此文件夹，然后重新运行脚本。")
    else:
        organize_pdfs_into_folders(
            source_dir=SOURCE_DIRECTORY,
            native_target_dir=NATIVE_PDF_DIRECTORY,
            scanned_target_dir=SCANNED_PDF_DIRECTORY
        )