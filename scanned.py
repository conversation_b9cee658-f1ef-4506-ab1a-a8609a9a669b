import os
import json
from pathlib import Path
from paddleocr import PPStructureV3
from PIL import Image

def process_and_organize_outputs(input_dir="input", output_dir="output_scanned"):

    # --- 1. 初始化模型 ---
    print("正在初始化 PP-StructureV3 模型...")
    pipeline = PPStructureV3(
        use_doc_orientation_classify=True,
        use_table_recognition=True,
        use_doc_unwarping=False,
        use_seal_recognition=False
    )
    print("模型初始化完成。")

    # --- 2. 准备目录 ---
    if not os.path.exists(input_dir):
        print(f"错误：输入目录 '{input_dir}' 不存在。")
        return
    os.makedirs(output_dir, exist_ok=True)

    pdf_files = [f for f in os.listdir(input_dir) if f.lower().endswith('.pdf')]
    if not pdf_files:
        print(f"在 '{input_dir}' 目录中没有找到PDF文件。")
        return
    print(f"找到 {len(pdf_files)} 个PDF文件待处理。")

    for filename in pdf_files:
        pdf_path = os.path.join(input_dir, filename)
        doc_name = Path(pdf_path).stem
        print(f"\n--- 开始处理文档: {filename} ---")

        doc_output_dir = os.path.join(output_dir, doc_name)
        os.makedirs(doc_output_dir, exist_ok=True)

        jsonl_save_path = os.path.join(doc_output_dir, f"{doc_name}.jsonl")
        md_save_path = os.path.join(doc_output_dir, f"{doc_name}.md")

        try:
            results_iterator = pipeline.predict_iter(input=pdf_path)
            all_pages_markdown_info = []

            with open(jsonl_save_path, 'w', encoding='utf-8') as f_jsonl:
                page_count = 0
                for page_result in results_iterator:
                    page_count += 1
                    print(f"  正在处理第 {page_count} 页...")

                    # 任务一：生成JSONL
                    parsed_blocks = page_result["parsing_res_list"]
                    for i, block in enumerate(parsed_blocks):
                        
                        # ===================== BUG修复处 START =====================
                        # 使用更清晰、更健壮的逻辑来填充记录
                        
                        # 1. 先初始化记录，内容字段默认为空
                        record = {
                            "id": f"{doc_name}_p{page_count}_b{i}",
                            "source_doc": filename,
                            "page_num": page_count,
                            "block_type": block.label,
                            "block_type_confidence":None,
                            "content": None,
                            "html_content": None,
                            "content_confidnece":None,
                            "reading_order": i,
                            "bbox": block.bbox,
                            "image_path": None,
                        }

                        # 2. 获取原始块内容
                        block_content = block.content
                        
                        # 3. 根据块类型，将内容放入正确的字段
                        if record["block_type"] == 'table':
                            # 如果是表格，将HTML内容放入 html_content
                            record['html_content'] = block_content
                        else:
                            # 对于所有其他类型（文本、标题等），将文本内容放入 content
                            record['content'] = block_content
                        
                        # ===================== BUG修复处 END =====================

                        # 图片路径提取逻辑（保持不变）
                        if record["block_type"] in ['image', 'chart', 'figure']:
                            for img_name, img_data in page_result.markdown.get("markdown_images", {}).items():
                                if record["block_type"] in img_name: # 简化匹配
                                    img_dir = os.path.join(doc_output_dir, "imgs")
                                    os.makedirs(img_dir, exist_ok=True)
                                    relative_img_path = os.path.join("imgs", os.path.basename(img_name))
                                    abs_img_save_path = os.path.join(doc_output_dir, relative_img_path)

                                    if isinstance(img_data, Image.Image):
                                        img_data.save(abs_img_save_path)
                                    record['image_path'] = relative_img_path.replace("\\", "/")
                                    break
                        
                        f_jsonl.write(json.dumps(record, ensure_ascii=False) + '\n')
                    
                    # 任务二：收集Markdown信息用于生成预览文件
                    markdown_info = page_result.markdown
                    all_pages_markdown_info.append(markdown_info)
                    
                    if "markdown_images" in markdown_info:
                        for img_name, img_data in markdown_info["markdown_images"].items():
                            image_save_path = os.path.join(doc_output_dir, img_name)
                            image_dir = os.path.dirname(image_save_path)
                            os.makedirs(image_dir, exist_ok=True)
                            if isinstance(img_data, Image.Image):
                                img_data.save(image_save_path)

            print("JSONL 文件已生成。现在开始整合Markdown预览文件...")

            # 任务三：拼接并保存完整的Markdown文件
            if all_pages_markdown_info:
                full_markdown_content = pipeline.concatenate_markdown_pages(all_pages_markdown_info)
                with open(md_save_path, 'w', encoding='utf-8') as f:
                    f.write(full_markdown_content)
                print(f"Markdown 预览文件已保存到: {md_save_path}")

            print(f"--- 文档: {filename} 处理完成 ---")

        except Exception as e:
            print(f"处理文档 {filename} 时发生严重错误: {e}")
            import traceback
            traceback.print_exc()

    print("\n所有任务已完成。")

if __name__ == "__main__":
    process_and_organize_outputs()