1. 全局功能开关
这些参数用于控制是否启用特定的文档理解功能。
参数	含义	默认值
use_doc_orientation_classify	是否使用文档图像方向分类功能（例如，判断图片是否需要旋转90/180/270度）。	None
use_doc_unwarping	是否对弯曲或变形的文档图像进行矫正（反畸变）。	None
use_textline_orientation	是否使用文本行方向分类功能（判断文本行是水平还是有一定角度）。	None
use_table_recognition	(核心功能) 是否启用表格识别。	None
use_formula_recognition	(核心功能) 是否启用公式识别。	None
use_chart_recognition	(核心功能) 是否启用图表识别。	None
use_seal_recognition	(核心功能) 是否启用印章识别（包括印章检测和印章内文字识别）。	None
use_region_detection	是否启用特定区域检测功能。	None
2. 模型配置参数
这些参数允许您为流水线中的每个子任务指定具体的模型或配置。
参数	含义	默认值
layout_detection_model_name	版面分析模型的名称。	None
layout_detection_model_dir	版面分析模型的本地路径。	None
layout_threshold	版面分析的得分阈值，低于此值的检测框将被过滤。	None
layout_nms	是否在版面分析后处理中使用非极大值抑制（NMS）。	None
layout_unclip_ratio	版面检测框的扩张系数，用于扩大检测框区域。	None
layout_merge_bboxes_mode	重叠检测框的过滤和合并方法。	None
参数	含义	默认值
chart_recognition_model_name	图表识别模型的名称。	None
chart_recognition_model_dir	图表识别模型的本地路径。	None
chart_recognition_batch_size	图表识别的批处理大小。	None
参数	含义	默认值
doc_orientation_classify_model_name	文档方向分类模型的名称。	None
doc_orientation_classify_model_dir	文档方向分类模型的本地路径。	None
doc_unwarping_model_name	文档图像矫正模型的名称。	None
doc_unwarping_model_dir	文档图像矫正模型的本地路径。	None
参数	含义	默认值
text_detection_model_name	文本检测模型的名称。	None
text_detection_model_dir	文本检测模型的本地路径。	None
text_det_limit_side_len	文本检测模型输入图像的最长边限制。	None
text_det_limit_type	最长边限制的应用方式（'max' 或 'min'）。	None
text_det_thresh	文本检测中，判断像素是否为文本的前景/背景分割阈值。	None
text_det_box_thresh	文本检测中，判断检测框是否有效的得分阈值。	None
text_det_unclip_ratio	文本检测框的扩张系数，用于更完整地包裹文本区域。	None
textline_orientation_model_name	文本行方向分类模型的名称。	None
textline_orientation_model_dir	文本行方向分类模型的本地路径。	None
textline_orientation_batch_size	文本行方向分类的批处理大小。	None
text_recognition_model_name	文本识别模型的名称。	None
text_recognition_model_dir	文本识别模型的本地路径。	None
text_recognition_batch_size	文本识别的批处理大小。	None
text_rec_score_thresh	文本识别结果的置信度阈值，低于此值的识别结果被过滤。	None
参数	含义	默认值
table_classification_model_name	表格分类（如有线/无线表格）模型的名称。	None
table_classification_model_dir	表格分类模型的本地路径。	None
wired_table_structure_recognition_model_name	有线表格结构识别模型的名称。	None
wired_table_structure_recognition_model_dir	有线表格结构识别模型的本地路径。	None
wireless_table_structure_recognition_model_name	无线表格结构识别模型的名称。	None
wireless_table_structure_recognition_model_dir	无线表格结构识别模型的本地路径。	None
wired_table_cells_detection_model_name	有线表格单元格检测模型的名称。	None
wired_table_cells_detection_model_dir	有线表格单元格检测模型的本地路径。	None
wireless_table_cells_detection_model_name	无线表格单元格检测模型的名称。	None
wireless_table_cells_detection_model_dir	无线表格单元格检测模型的本地路径。	None
table_orientation_classify_model_name	表格方向分类模型的名称。	None
table_orientation_classify_model_dir	表格方向分类模型的本地路径。	None
参数	含义	默认值
seal_text_detection_model_name	印章内文字检测模型的名称。	None
seal_text_detection_model_dir	印章内文字检测模型的本地路径。	None
seal_det_limit_side_len	印章内文字检测输入图像的最长边限制。	None
seal_det_limit_type	最长边限制的应用方式。	None
seal_det_thresh	印章内文字检测的前景/背景分割阈值。	None
seal_det_box_thresh	印章内文字检测的检测框得分阈值。	None
seal_det_unclip_ratio	印章内文字检测框的扩张系数。	None
seal_text_recognition_model_name	印章内文字识别模型的名称。	None
seal_text_recognition_model_dir	印章内文字识别模型的本地路径。	None
seal_text_recognition_batch_size	印章内文字识别的批处理大小。	None
seal_rec_score_thresh	印章内文字识别结果的置信度阈值。	None
参数	含义	默认值
formula_recognition_model_name	公式识别模型的名称。	None
formula_recognition_model_dir	公式识别模型的本地路径。	None
formula_recognition_batch_size	公式识别的批处理大小。	None
二、预测参数 (predict 和 predict_iter)
这些参数在您调用 predict 方法时传入，可以临时覆盖初始化时设置的默认行为，为单次预测提供高度的灵活性。
参数	含义	默认值
input	必需参数。输入的待分析数据，可以是单张图片路径、图片路径列表、Numpy数组等。	无
use_doc_orientation_classify	是否在本次预测中使用文档方向分类。	False
use_doc_unwarping	是否在本次预测中使用图像矫正。	False
use_textline_orientation	是否在本次预测中使用文本行方向分类。	None
use_seal_recognition	是否在本次预测中使用印章识别。	None
use_table_recognition	是否在本次预测中使用表格识别。	None
use_formula_recognition	是否在本次预测中使用公式识别。	None
use_chart_recognition	是否在本次预测中使用图表识别。	False
use_region_detection	是否在本次预测中使用区域检测。	None
use_wired_table_cells_trans_to_html	对于有线表格，是否将单元格识别结果转换为HTML。	False
use_wireless_table_cells_trans_to_html	对于无线表格，是否将单元格识别结果转换为HTML。	False
use_table_orientation_classify	是否在表格识别流程中使用表格方向分类。	True
use_ocr_results_with_table_cells	是否将表格单元格内的OCR结果与整体OCR结果结合。	True
use_e2e_wired_table_rec_model	是否使用端到端的有线表格识别模型。	False
use_e2e_wireless_table_rec_model	是否使用端到端的无线表格识别模型。	True


jsonl输出：
id: 全局唯一ID。
source_doc: 来源于哪个PDF文件。
page_num: 在PDF中的页码。
block_type: 内容块的类型（如text, title, table）。
content: 纯文本内容。
html_content: 如果是表格，这里会存放其完整的HTML代码。
bbox: 内容块在页面上的坐标位置。